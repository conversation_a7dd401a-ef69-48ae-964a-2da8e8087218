# 邮件批量分析功能说明

## 功能概述

邮件分析系统现在支持两种分析模式：

1. **优化批量模式**（推荐）：一次提交多封邮件给Gemini分析，显著减少API调用次数
2. **传统模式**：逐个分析邮件，每封邮件单独调用一次API

## 优化批量模式的优势

- **减少API调用次数**：原来分析10封邮件需要10次API调用，现在只需要1次
- **提高分析效率**：减少网络延迟和请求开销
- **降低成本**：减少API调用费用
- **更好的上下文理解**：Gemini可以在同一次请求中比较多封邮件

## 配置说明

### 配置文件设置

在 `email_config.ini` 文件的 `[ANALYSIS]` 部分：

```ini
[ANALYSIS]
# 批处理大小 (一次分析的邮件数量，建议5-15)
batch_size = 10

# 请求间隔时间 (秒，批次之间的延迟)
delay_between_requests = 1.0
```

### 命令行参数

```bash
# 使用默认批量模式（推荐）
python main.py --limit 20

# 自定义批次大小
python main.py --limit 20 --batch-size 5

# 使用传统逐个分析模式
python main.py --limit 20 --use-traditional

# 查看所有参数
python main.py --help
```

## 使用示例

### 示例1：默认批量分析
```bash
python main.py --limit 30
```
- 获取30封邮件
- 使用配置文件中的批次大小（默认10）
- 分3批进行分析，每批10封邮件
- 总共调用3次Gemini API

### 示例2：自定义批次大小
```bash
python main.py --limit 30 --batch-size 15
```
- 获取30封邮件
- 每批分析15封邮件
- 分2批进行分析
- 总共调用2次Gemini API

### 示例3：传统模式（兼容性）
```bash
python main.py --limit 10 --use-traditional
```
- 获取10封邮件
- 逐个分析每封邮件
- 总共调用10次Gemini API

## 批次大小建议

| 邮件数量 | 建议批次大小 | 说明 |
|---------|-------------|------|
| 1-5封   | 5           | 小批量处理 |
| 6-20封  | 10          | 标准批量处理 |
| 21-50封 | 15          | 大批量处理 |
| 50+封   | 20          | 超大批量处理 |

**注意**：
- 批次太小：无法充分利用批量优势
- 批次太大：可能超出API请求限制或影响分析质量
- 建议根据实际使用情况调整

## 错误处理

系统具备完善的错误处理机制：

1. **API调用失败**：自动为失败的邮件创建默认分析结果
2. **JSON解析错误**：记录错误并提供备用结果
3. **部分邮件分析失败**：只影响当前批次，不影响其他批次
4. **网络超时**：自动重试或降级到传统模式

## 性能对比

### API调用次数对比
| 邮件数量 | 传统模式 | 批量模式(批次=10) | 节省比例 |
|---------|---------|------------------|----------|
| 10封    | 10次    | 1次              | 90%      |
| 30封    | 30次    | 3次              | 90%      |
| 50封    | 50次    | 5次              | 90%      |
| 100封   | 100次   | 10次             | 90%      |

### 时间对比（估算）
假设每次API调用需要2秒：
- 传统模式分析30封邮件：30 × 2秒 = 60秒
- 批量模式分析30封邮件：3 × 2秒 = 6秒
- **时间节省：90%**

## 环境变量支持

可以通过环境变量覆盖配置：

```bash
# 设置批次大小
export ANALYSIS_BATCH_SIZE=15

# 设置请求延迟
export ANALYSIS_DELAY=2.0

# 运行程序
python main.py --limit 30
```

## 故障排除

### 常见问题

1. **批量分析结果不完整**
   - 检查Gemini API响应是否完整
   - 尝试减小批次大小
   - 查看日志文件了解详细错误

2. **API调用频率限制**
   - 增加 `delay_between_requests` 值
   - 减小 `batch_size` 值

3. **内存使用过高**
   - 减小批次大小
   - 限制邮件内容长度

### 日志查看

```bash
# 查看详细日志
tail -f email_analysis.log

# 查看错误日志
grep ERROR email_analysis.log
```

## 最佳实践

1. **首次使用**：建议从小批次开始测试（batch_size=5）
2. **生产环境**：根据邮件量和API限制调整批次大小
3. **监控日志**：定期检查分析质量和错误率
4. **备份策略**：重要邮件建议保留传统模式作为备选

## 🔄 智能模型切换功能

### 功能概述

系统现在支持智能模型切换功能，当主模型（如gemini-2.5-pro）出现过载错误时，会自动切换到备用模型（如gemini-2.5-flash）继续分析。

### 自动切换条件

系统会在检测到以下错误时自动切换模型：
- `503 UNAVAILABLE` 错误
- `The model is overloaded` 消息
- 包含 `overloaded` 关键词的错误
- 其他模型过载相关错误

### 配置方法

在 `email_config.ini` 文件中配置：

```ini
[ANALYSIS]
# 主模型
model_name = gemini-2.5-pro

# 备用模型（当主模型过载时自动切换）
fallback_model = gemini-2.5-flash
```

### 环境变量配置

```bash
# 设置主模型
export GEMINI_MODEL=gemini-2.5-pro

# 设置备用模型
export GEMINI_FALLBACK_MODEL=gemini-2.5-flash
```

### 错误处理改进

- **错误识别**：系统能正确识别分析失败的邮件
- **评分修正**：分析失败的邮件评分设为0分，避免误判为重要邮件
- **模型记录**：每封邮件的分析结果都会记录使用的模型
- **智能切换**：一旦检测到模型过载，后续所有分析都使用备用模型

### 分析结果增强

每个分析结果现在包含以下新字段：

```json
{
  "analyzed_by_model": "gemini-2.5-pro",
  "importance_score": 8,
  "importance_level": "高",
  "error": false
}
```

对于分析失败的邮件：

```json
{
  "analyzed_by_model": "gemini-2.5-pro",
  "importance_score": 0,
  "importance_level": "未知",
  "category": "分析失败",
  "summary": "分析失败",
  "reason": "分析过程出错: 503 UNAVAILABLE",
  "error": true
}
```

### 报告增强

分析报告现在包含：
- 模型使用统计
- 分析成功/失败数量
- 每封重要邮件的分析模型信息

## 版本兼容性

- 新功能完全向后兼容
- 现有脚本无需修改即可使用
- 可随时切换回传统模式
- 自动模型切换功能默认启用
