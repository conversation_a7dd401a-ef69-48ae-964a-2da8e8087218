# 邮件分析系统

基于Python和Gemini大模型的智能邮件分析系统，能够自动获取邮件并识别重要邮件。

## 功能特性

- 🔗 **多邮箱支持**: 支持QQ邮箱、163邮箱、Gmail、Outlook等主流邮箱
- 🤖 **AI智能分析**: 使用Gemini大模型分析邮件内容，识别重要邮件
- 🚀 **批量分析优化**: 一次提交多封邮件分析，显著减少API调用次数（节省90%调用量）
- 🔄 **智能模型切换**: 当主模型过载时自动切换到备用模型，确保服务连续性
- 📊 **详细报告**: 生成详细的分析报告和统计信息
- 🔒 **安全配置**: 支持环境变量和配置文件，保护敏感信息
- 📝 **完整日志**: 详细的日志记录，便于问题排查
- ⚙️ **灵活配置**: 丰富的配置选项，满足不同需求

## 安装依赖

```bash
pip install google-genai imaplib-ssl configparser
```

## 快速开始

### 1. 配置邮箱

复制配置文件模板：
```bash
cp email_config.ini.example email_config.ini
```

编辑 `email_config.ini` 文件，填入您的邮箱信息：

```ini
[EMAIL]
provider = qq
server = imap.qq.com
port = 993
username = <EMAIL>
password = your_auth_code
folder = INBOX
```

**重要提醒**：
- QQ邮箱需要使用授权码，不是登录密码
- 授权码获取方法：QQ邮箱设置 → 账户 → POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务 → 生成授权码

### 2. 设置API密钥

设置Gemini API密钥环境变量：

**Windows:**
```cmd
set GEMINI_API_KEY=your_gemini_api_key
```

**Linux/Mac:**
```bash
export GEMINI_API_KEY=your_gemini_api_key
```

### 3. 运行系统

```bash
python main.py
```

## 使用方法

### 基本用法

```bash
# 分析最新10封邮件
python main.py

# 分析最新20封邮件
python main.py --limit 20

# 分析指定文件夹的邮件
python main.py --folder "重要邮件"

# 使用自定义配置文件
python main.py --config my_config.ini

# 设置最低重要性评分
python main.py --min-score 8

# 使用批量分析（推荐，减少API调用）
python main.py --batch-size 10

# 使用传统逐个分析模式
python main.py --use-traditional

# 检查配置状态
python main.py --check-config
```

### 环境变量配置

您也可以使用环境变量来配置系统，环境变量优先级高于配置文件：

```bash
# 邮箱配置
export EMAIL_PROVIDER=qq
export EMAIL_SERVER=imap.qq.com
export EMAIL_PORT=993
export EMAIL_USERNAME=<EMAIL>
export EMAIL_PASSWORD=your_auth_code
export EMAIL_FOLDER=INBOX

# 分析配置
export GEMINI_MODEL=gemini-2.5-pro
export GEMINI_FALLBACK_MODEL=gemini-2.5-flash
export ANALYSIS_BATCH_SIZE=10
export ANALYSIS_DELAY=1.0
export MIN_IMPORTANCE_SCORE=7

# 输出配置
export SAVE_RESULTS=true
export RESULTS_FILE=analysis_results.json
export GENERATE_REPORT=true
export REPORT_FILE=analysis_report.txt
```

## 批量分析功能

### 🚀 新功能亮点

系统现在支持**批量分析模式**，可以一次提交多封邮件给Gemini分析，相比传统的逐个分析模式：

- **减少90%的API调用次数**
- **提高分析效率**
- **降低使用成本**
- **更好的上下文理解**

### 使用方法

```bash
# 默认批量模式（推荐）
python main.py --limit 30

# 自定义批次大小
python main.py --limit 30 --batch-size 15

# 传统模式（兼容性）
python main.py --limit 30 --use-traditional
```

### 性能对比

| 邮件数量 | 传统模式API调用 | 批量模式API调用 | 节省比例 |
|---------|---------------|---------------|----------|
| 10封    | 10次          | 1次           | 90%      |
| 30封    | 30次          | 3次           | 90%      |
| 50封    | 50次          | 5次           | 90%      |

### 🔄 智能模型切换

当主模型（gemini-2.5-pro）出现过载时，系统会自动切换到备用模型（gemini-2.5-flash）：

```bash
# 配置主模型和备用模型
export GEMINI_MODEL=gemini-2.5-pro
export GEMINI_FALLBACK_MODEL=gemini-2.5-flash
```

**自动切换特性**：
- 检测到503错误或模型过载时自动切换
- 分析失败的邮件评分设为0分，避免误判
- 每封邮件记录使用的分析模型
- 确保服务连续性和分析准确性

详细说明请参考：[批量分析功能说明.md](批量分析功能说明.md)

## 输出说明

### 分析结果文件 (JSON格式)

包含每封邮件的详细分析结果：
- 重要性评分 (1-10)
- 重要性级别 (高/中/低)
- 邮件分类 (工作/个人/营销等)
- 关键词提取
- 内容摘要
- 分析原因
- 是否需要行动
- 紧急程度

### 分析报告文件 (文本格式)

包含：
- 总体统计信息
- 邮件分类统计
- 重要邮件详情
- 处理建议

## 常见问题

### Q: QQ邮箱连接失败？
A: 请确保：
1. 已开启IMAP服务
2. 使用授权码而不是登录密码
3. 网络连接正常

### Q: Gemini API调用失败？
A: 请检查：
1. GEMINI_API_KEY环境变量是否正确设置
2. API密钥是否有效
3. 网络是否能访问Google服务

### Q: 邮件内容乱码？
A: 系统已自动处理常见编码问题，如仍有问题请检查邮件原始编码。

### Q: 分析结果不准确？
A: 可以通过以下方式优化：
1. 调整最低重要性评分
2. 修改分析提示词
3. 增加训练样本

## 文件结构

```
邮件分析/
├── main.py                    # 主程序
├── email_fetcher.py          # 邮件获取模块
├── email_analyzer.py         # 邮件分析模块
├── config_manager.py         # 配置管理模块
├── email_config.ini.example  # 配置文件模板
├── README.md                 # 使用说明
└── 输出文件/
    ├── analysis_results_*.json  # 分析结果
    ├── analysis_report_*.txt    # 分析报告
    └── email_analysis.log       # 日志文件
```

## 安全建议

1. **不要在代码中硬编码密码**
2. **使用环境变量存储敏感信息**
3. **定期更换邮箱授权码**
4. **保护API密钥安全**
5. **定期清理日志文件**

## 许可证

本项目仅供学习和个人使用，请遵守相关法律法规和服务条款。
