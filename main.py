"""
邮件分析系统主程序
整合邮件获取、大模型分析等功能
"""

import os
import sys
import json
import logging
from datetime import datetime
from typing import List, Dict
import argparse

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from email_fetcher import EmailFetcher, get_email_server_config
from email_analyzer import EmailAnalyzer
from config_manager import ConfigManager


def setup_logging(config: Dict):
    """
    设置日志配置
    
    Args:
        config: 日志配置
    """
    log_level = getattr(logging, config['level'].upper(), logging.INFO)
    
    # 配置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 文件处理器
    file_handler = logging.FileHandler(
        config['log_file'], 
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    file_handler.setLevel(log_level)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    console_handler.setLevel(log_level)
    
    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='邮件分析系统')
    parser.add_argument('--config', '-c', default='email_config.ini', 
                       help='配置文件路径')
    parser.add_argument('--limit', '-l', type=int, default=10, 
                       help='获取邮件数量限制')
    parser.add_argument('--folder', '-f', default='INBOX', 
                       help='邮件文件夹')
    parser.add_argument('--check-config', action='store_true', 
                       help='检查配置并退出')
    parser.add_argument('--min-score', type=int,
                       help='最低重要性评分（覆盖配置文件设置）')
    parser.add_argument('--batch-size', type=int,
                       help='批量分析时每批处理的邮件数量（覆盖配置文件设置）')
    parser.add_argument('--use-traditional', action='store_true',
                       help='使用传统的逐个分析模式（而非优化的批量模式）')
    
    args = parser.parse_args()
    
    # 初始化配置管理器
    config_manager = ConfigManager(args.config)
    
    # 检查配置模式
    if args.check_config:
        config_manager.print_config_status()
        return
    
    # 获取配置
    email_config = config_manager.get_email_config()
    analysis_config = config_manager.get_analysis_config()
    logging_config = config_manager.get_logging_config()
    output_config = config_manager.get_output_config()
    
    # 设置日志
    setup_logging(logging_config)
    logger = logging.getLogger(__name__)
    
    logger.info("="*50)
    logger.info("邮件分析系统启动")
    logger.info("="*50)
    
    try:
        # 验证配置
        if not config_manager.validate_email_config(email_config):
            logger.error("邮箱配置无效，请检查配置文件或环境变量")
            return
        
        if not config_manager.check_gemini_api_key():
            logger.error("请设置GEMINI_API_KEY环境变量")
            return
        
        # 使用命令行参数覆盖配置
        if args.min_score:
            analysis_config['min_score'] = args.min_score
        if args.batch_size:
            analysis_config['batch_size'] = args.batch_size
        
        # 初始化邮件获取器
        logger.info("初始化邮件获取器...")
        fetcher = EmailFetcher(
            server=email_config['server'],
            port=email_config['port'],
            username=email_config['username'],
            password=email_config['password']
        )
        
        # 连接邮箱
        logger.info("连接邮箱...")
        if not fetcher.connect():
            logger.error("连接邮箱失败")
            return
        
        try:
            # 获取邮件
            folder = args.folder or email_config['folder']
            limit = args.limit
            
            logger.info(f"从文件夹 '{folder}' 获取最新 {limit} 封邮件...")
            emails = fetcher.fetch_emails(folder=folder, limit=limit)
            
            if not emails:
                logger.warning("没有获取到邮件")
                return
            
            logger.info(f"成功获取 {len(emails)} 封邮件")
            
            # 初始化分析器
            logger.info("初始化邮件分析器...")
            analyzer = EmailAnalyzer(model_name=analysis_config['model_name'])
            
            # 批量分析邮件
            logger.info("开始分析邮件...")
            use_optimized = not args.use_traditional
            analysis_results = analyzer.analyze_emails_batch(
                emails,
                delay=analysis_config['delay'],
                batch_size=analysis_config['batch_size'],
                use_optimized=use_optimized
            )

            if use_optimized:
                logger.info(f"使用优化批量模式，批次大小: {analysis_config['batch_size']}")
            else:
                logger.info("使用传统逐个分析模式")
            
            if not analysis_results:
                logger.warning("没有分析结果")
                return
            
            # 筛选重要邮件
            important_emails = analyzer.filter_important_emails(
                analysis_results, 
                min_score=analysis_config['min_score']
            )
            
            # 生成报告
            report = analyzer.generate_summary_report(analysis_results)
            
            # 输出结果
            print("\n" + "="*60)
            print("📧 邮件分析结果")
            print("="*60)
            print(report)
            
            # 保存结果
            if output_config['save_results']:
                results_file = output_config['results_file']
                logger.info(f"保存分析结果到: {results_file}")
                
                # 添加时间戳到文件名
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                name, ext = os.path.splitext(results_file)
                timestamped_file = f"{name}_{timestamp}{ext}"
                
                with open(timestamped_file, 'w', encoding='utf-8') as f:
                    json.dump({
                        'timestamp': datetime.now().isoformat(),
                        'total_emails': len(emails),
                        'analyzed_emails': len(analysis_results),
                        'important_emails': len(important_emails),
                        'config': {
                            'folder': folder,
                            'limit': limit,
                            'min_score': analysis_config['min_score']
                        },
                        'results': analysis_results
                    }, f, ensure_ascii=False, indent=2)
                
                print(f"\n💾 分析结果已保存到: {timestamped_file}")
            
            # 保存报告
            if output_config['generate_report']:
                report_file = output_config['report_file']
                logger.info(f"保存分析报告到: {report_file}")
                
                # 添加时间戳到文件名
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                name, ext = os.path.splitext(report_file)
                timestamped_report = f"{name}_{timestamp}{ext}"
                
                with open(timestamped_report, 'w', encoding='utf-8') as f:
                    f.write(f"邮件分析报告\n")
                    f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"分析配置: 文件夹={folder}, 数量={limit}, 最低评分={analysis_config['min_score']}, 批次大小={analysis_config['batch_size']}, 优化模式={'是' if use_optimized else '否'}\n")
                    f.write("\n" + report)
                
                print(f"📄 分析报告已保存到: {timestamped_report}")
            
            # 显示重要邮件提醒
            if important_emails:
                print(f"\n🚨 发现 {len(important_emails)} 封重要邮件，请及时处理！")
                for i, email in enumerate(important_emails[:5], 1):
                    print(f"  {i}. {email.get('email_subject', '无主题')[:50]}...")
                    if email.get('urgency', '').lower() in ['紧急', 'urgent']:
                        print(f"     ⚠️ 紧急邮件！")
            else:
                print(f"\n✅ 没有发现重要邮件（评分 >= {analysis_config['min_score']}）")
            
        finally:
            # 断开邮箱连接
            fetcher.disconnect()
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
        print("\n程序已中断")
    except Exception as e:
        logger.error(f"程序执行出错: {e}", exc_info=True)
        print(f"\n❌ 程序执行出错: {e}")
    finally:
        logger.info("邮件分析系统结束")
        logger.info("="*50)


if __name__ == "__main__":
    main()
